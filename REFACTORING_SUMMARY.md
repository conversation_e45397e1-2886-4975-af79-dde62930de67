# 圖表互動視窗重構總結

## 問題描述

在原始程式碼中，存在重複的互動視窗圖表實作：

1. **AzureCosmos.razor** 中有一個獨立的 `BS5Modal`，包含完整的 `AzureChart` 組件
2. **MyCard_dark2.razor** 中也有互動視窗圖表功能，但內容只是佔位符 "我是圖表唷"
3. 這造成了程式碼重複和維護困難

## 解決方案

### 1. 修改 MyCard_dark2.razor

- 將互動視窗圖表的內容從佔位符改為動態內容
- 新增 `ModalChartContent` 參數來接收圖表內容
- 當沒有提供圖表內容時，顯示友好的提示訊息

```razor
<!-- 互動視窗圖表 -->
<BS5Modal Id="@($"chart-{ModalId}")" Title="@($"{Title} - 統計圖表")">
    <ButtonContent>
        <i class="fa-solid fa-expand"></i>
    </ButtonContent>

    <ChildContent>
        @if (ModalChartContent != null)
        {
            @ModalChartContent
        }
        else
        {
            <div class="text-center p-4">
                <i class="fa-solid fa-chart-line fa-3x text-muted mb-3"></i>
                <p class="text-muted">圖表內容尚未設定</p>
            </div>
        }
    </ChildContent>
</BS5Modal>
```

### 2. 修改 AzureChart.razor

- 新增 `ModalChartContent` 屬性，動態生成互動視窗的圖表內容
- 實作 `CreateModalChartContent()` 方法，使用 RenderFragment 動態建構圖表
- 將 `ModalChartContent` 傳遞給 `MyCard_dark2` 組件

```csharp
// 互動視窗圖表內容
private RenderFragment? ModalChartContent => CreateModalChartContent();

// 創建互動視窗圖表內容
private RenderFragment? CreateModalChartContent()
{
    if (isLoading || !string.IsNullOrEmpty(errorMessage) || internalChartData == null || !internalChartData.Any())
    {
        return null;
    }

    return builder =>
    {
        // 動態建構 ApexChart 和 ApexPointSeries 組件
        // 支援單系列和多系列圖表
        // 支援資料標籤顯示設定
    };
}
```

### 3. 移除重複程式碼

- 從 **AzureCosmos.razor** 中移除獨立的 `BS5Modal` 互動視窗圖表
- 所有圖表現在都統一使用 `MyCard_dark2` 的互動視窗功能

## 改進效果

### 優點

1. **消除程式碼重複**：不再需要在每個頁面中重複定義互動視窗圖表
2. **統一的使用者體驗**：所有圖表的互動視窗都有一致的外觀和行為
3. **更好的維護性**：互動視窗功能集中在 `MyCard_dark2` 組件中，易於維護
4. **靈活性**：支援動態內容，可以根據圖表類型自動調整
5. **向後相容**：現有的圖表功能不受影響

### 技術特點

1. **動態 RenderFragment**：使用 Blazor 的 RenderFragment 動態建構圖表組件
2. **支援多種圖表類型**：單系列、多系列、混合圖表都能正確顯示
3. **錯誤處理**：當圖表資料不可用時，顯示適當的提示訊息
4. **效能優化**：只在需要時才建構互動視窗內容

## 使用方式

現在所有使用 `AzureChart` 的地方都會自動獲得互動視窗圖表功能，無需額外設定：

```razor
<AzureChart TContainer="UA_LibModel"
            TItem="UA_LibData"
            ContainerType="@ContainerType.UA_Lib"
            Title="⭐ 113學年度重點館藏資源統計"
            ChartOptions="@Lib_ChEn_PieChartOpt"
            ChartType="SeriesType.Pie"
            SeriesName="藏書比例"
            XValueSelector="@(e => e.SchoolName)"
            YValueSelector="@(e => (decimal?)e.Lib_Ch_Total)"
            ModalId="Lib_ChEn_Pie"
            PreChartData="@pre_UALibData"
            TargetSchoolName="@filterYuntech"
            TableData="@filteredLibChTotalTableData"
            OnDataLoaded="@Lib_ChartLoad"
            PieChartFields="@libPieChartFields"/>
```

互動視窗圖表會自動包含與主圖表相同的資料和設定，提供更大的檢視區域。

## 檔案異動清單

1. **Components/Pages/Common/MyCard_dark2.razor** - 修改互動視窗圖表內容
2. **Components/Pages/MyChart/AzureChart.razor** - 新增動態圖表內容生成
3. **Components/Pages/AzureCosmos.razor** - 移除重複的互動視窗圖表

## 測試建議

1. 確認所有使用 `AzureChart` 的頁面都能正常顯示互動視窗圖表
2. 測試不同類型的圖表（圓餅圖、長條圖、折線圖、面積圖等）
3. 測試多系列圖表的互動視窗顯示
4. 確認錯誤狀態下的提示訊息顯示正確
