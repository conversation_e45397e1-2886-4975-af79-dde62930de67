@using ApexCharts
@using System.Linq
@using System.Collections.Generic

@typeparam TItem where TItem : class

@if (isLoading)
{
    <div class="text-center mt-5">
        <div class="spinner-border mt-5" role="status">
        </div>
        <p class="mt-3">載入中...</p>
    </div>
}
else if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger">@errorMessage</div>
}
else if (ChartData == null || !ChartData.Any())
{
    <div class="alert alert-info">沒有可顯示的數據</div>
}
else
{
    <ApexChart TItem="TItem" Options="ChartOptions">
        @if (!MultiSeries)
        {
            @if (isShowDataLabels)
            {
                <ApexPointSeries TItem="TItem"
                                Name="@SeriesName"
                                Items="@ChartData"
                                XValue="@XValueSelector"
                                YValue="@YValueSelector"
                                SeriesType="@ChartType"
                                ShowDataLabels />
            }
            else
            {
                <ApexPointSeries TItem="TItem"
                                Name="@SeriesName"
                                Items="@ChartData"
                                XValue="@XValueSelector"
                                YValue="@YValueSelector"
                                SeriesType="@ChartType" />
            }
        }
        else
        {
            @if (ValueProperties != null)
            {
                @foreach (var prop in ValueProperties)
                {
                    var seriesType = GetSeriesType(prop.PropName);
                    
                    @if (isShowDataLabels)
                    {
                        <ApexPointSeries TItem="TItem"
                                        Name="@prop.DisplayName"
                                        Items="@ChartData"
                                        XValue="@XValueSelector"
                                        YValue="@(item => GetYValue(item, prop.PropName))"
                                        SeriesType="@seriesType"
                                        ShowDataLabels />
                    }
                    else
                    {
                        <ApexPointSeries TItem="TItem"
                                        Name="@prop.DisplayName"
                                        Items="@ChartData"
                                        XValue="@XValueSelector"
                                        YValue="@(item => GetYValue(item, prop.PropName))"
                                        SeriesType="@seriesType" />
                    }
                }
            }
        }
    </ApexChart>
}

@code {
    [Parameter] public List<TItem>? ChartData { get; set; }
    [Parameter] public ApexChartOptions<TItem>? ChartOptions { get; set; }
    [Parameter] public SeriesType ChartType { get; set; } = SeriesType.Bar;
    [Parameter] public string SeriesName { get; set; } = "數據";
    [Parameter] public Func<TItem, object>? XValueSelector { get; set; }
    [Parameter] public Func<TItem, decimal?>? YValueSelector { get; set; }
    [Parameter] public bool MultiSeries { get; set; } = false;
    [Parameter] public bool isShowDataLabels { get; set; } = true;
    [Parameter] public bool IsMixedChart { get; set; } = false;
    [Parameter] public Dictionary<string, SeriesType>? SeriesTypes { get; set; }
    [Parameter] public List<(string PropName, string DisplayName)>? ValueProperties { get; set; }
    [Parameter] public Func<TItem, string, decimal?>? YValueSelectorFunc { get; set; }

    private bool isLoading = false;
    private string? errorMessage;

    protected override void OnParametersSet()
    {
        // 驗證必要參數
        if (ChartData == null)
        {
            errorMessage = "圖表數據不能為空";
            return;
        }

        if (XValueSelector == null)
        {
            errorMessage = "X軸選擇器不能為空";
            return;
        }

        if (!MultiSeries && YValueSelector == null)
        {
            errorMessage = "Y軸選擇器不能為空";
            return;
        }

        if (MultiSeries && (ValueProperties == null || YValueSelectorFunc == null))
        {
            errorMessage = "多系列圖表需要提供 ValueProperties 和 YValueSelectorFunc";
            return;
        }

        errorMessage = null;
        base.OnParametersSet();
    }

    // 根據屬性名稱獲取圖表類型
    private SeriesType GetSeriesType(string propName)
    {
        // 如果是混合圖表且有對應的系列類型配置，則使用配置的類型
        if (IsMixedChart && SeriesTypes != null && SeriesTypes.ContainsKey(propName))
        {
            return SeriesTypes[propName];
        }

        // 否則使用預設圖表類型
        return ChartType;
    }

    // 獲取Y軸數值
    private decimal? GetYValue(TItem item, string propName)
    {
        if (YValueSelectorFunc != null)
        {
            return YValueSelectorFunc(item, propName);
        }

        // 如果沒有提供函數，嘗試使用反射
        var property = typeof(TItem).GetProperty(propName);
        if (property != null && property.PropertyType == typeof(decimal))
        {
            return (decimal?)property.GetValue(item);
        }

        return null;
    }
}
